import * as echarts from 'echarts'
import ynData from '@/assets/data/永宁县'
import mapBg from '@/assets/img/ditu.png'
// 地图打点图标
import mapIcon1 from '@/assets/img/mapIcon1.png'
import mapIcon2 from '@/assets/img/mapIcon2.png'
import mapIcon3 from '@/assets/img/mapIcon3.png'
echarts.registerMap('yn', ynData as any)
const mapIconList: any = [mapIcon1, mapIcon2, mapIcon3]



// 获取地图配置
export const getMapOption = () => {
  // 渐变层颜色 - 以#00E2B4为主色调的渐变
  const colorList: string[] = [
    '#00E2B4',
    '#00D4A8',
    '#00C69C',
    '#00B890',
    '#00AA84',
    '#009C78',
    '#008E6C',
    '#008060',
    '#007254',
    '#006448',
    '#00563C',
    '#004830'
  ]
  // 生成渐变图层
  const geoList: any = []
  for (let i = 1; i <= colorList.length; i++) {
    const mapOption: any = {
      map: 'yn',
      aspectScale: 0.85,
      emphasis: {
        disabled: true
      },
      z: 12 - i,
      layoutCenter: ['50%', `${i * 0.2 + 50}%`], //地图位置 - 适中的3D层次感
      layoutSize: '85%',
      itemStyle: {
        normal: {
          areaColor: colorList[i - 1],
          borderWidth: 0
        }
      }
    }
    if (i === colorList.length) {
      mapOption.itemStyle.normal.shadowColor = 'rgba(0, 226, 180, 0.6)'
      mapOption.itemStyle.normal.shadowBlur = 80
      mapOption.itemStyle.normal.shadowOffsetX = 4
      mapOption.itemStyle.normal.shadowOffsetY = 4
    }
    geoList.push(mapOption)
  }
  // 获取打点数据配置
  const pointSeriesData = getPointData()
  const option = {
    tooltip: {
      show: false  // 禁用全局tooltip
    },
    geo: [
      // 最外围发光边界
      {
        map: 'yn',
        aspectScale: 0.85,
        layoutCenter: ['50%', '50%'], //地图位置
        layoutSize: '85%',
        z: 12,
        emphasis: {
          disabled: true
        },
        itemStyle: {
          normal: {
            borderColor: '#00E2B4',
            borderWidth: 3,
            shadowColor: 'rgba(0, 226, 180, 0.6)',
            shadowBlur: 30,
            shadowOffsetX: 5,
            shadowOffsetY: 5
          }
        }
      },

      // 内部边界
      {
        map: 'yn',
        aspectScale: 0.85,
        layoutCenter: ['50%', '50%'], //地图位置
        layoutSize: '85%',
        z: 12,
        emphasis: {
          disabled: true
        },
        itemStyle: {
          normal: {
            areaColor: {
              image: mapBg
            },
            borderColor: '#00E2B4',
            borderWidth: 0.5
          }
        },
        label: {
          show: true,
          color: '#fff',
          fontSize: 14
        }
      },
      ...geoList
    ],
    series: [
      // 地图打点数据
      ...pointSeriesData
    ]
  }
  return option
}

// 生成地图打点数据
const getPointData = () => {
  const districtData: {
    name: string
    value: number
    point: number[]
    type: number // 图标类型 0,1,2 对应三种不同图标
    address: string
    details: Array<{
      time: string
      level: string
    }>
    image?: string
  }[] = [
    {
      name: '张三养殖场',
      value: 300,
      point: [106.253781, 38.28043],
      type: 0,
      address: '永宁县闽宁镇黄大组23号',
      details: [
        { time: '2025-8-28 19:22:39', level: '一级/二级' },
        { time: '2025-8-28 19:22:39', level: '一级/二级' },
        { time: '2025-8-28 19:22:39', level: '一级/二级' }
      ],
      image: '/src/assets/img/牛.png'
    },
    {
      name: '李四农场',
      value: 200,
      point: [106.15, 38.35],
      type: 1,
      address: '永宁县李俊镇新村15号',
      details: [
        { time: '2025-8-28 18:15:22', level: '二级/三级' },
        { time: '2025-8-28 17:30:15', level: '一级/二级' }
      ],
      image: '/src/assets/img/猪.png'
    },
    {
      name: '王五牧场',
      value: 129,
      point: [106.35, 38.25],
      type: 2,
      address: '永宁县闽宁镇东村8号',
      details: [
        { time: '2025-8-28 20:10:33', level: '三级/四级' },
        { time: '2025-8-28 19:45:18', level: '二级/三级' },
        { time: '2025-8-28 18:20:45', level: '一级/二级' }
      ],
      image: '/src/assets/img/羊.png'
    },
    {
      name: '赵六养殖基地',
      value: 107,
      point: [106.20, 38.20],
      type: 0,
      address: '永宁县望洪镇南街12号',
      details: [
        { time: '2025-8-28 16:55:28', level: '一级/二级' },
        { time: '2025-8-28 15:30:12', level: '二级/三级' }
      ],
      image: '/src/assets/img/牛.png'
    },
    {
      name: '孙七农业园',
      value: 86,
      point: [106.30, 38.30],
      type: 1,
      address: '永宁县杨和镇北区5号',
      details: [
        { time: '2025-8-28 21:05:15', level: '一级/二级' }
      ],
      image: '/src/assets/img/猪.png'
    },
    {
      name: '周八牧业',
      value: 95,
      point: [106.18, 38.32],
      type: 2,
      address: '永宁县胜利乡西村20号',
      details: [
        { time: '2025-8-28 14:20:35', level: '二级/三级' },
        { time: '2025-8-28 13:15:22', level: '一级/二级' }
      ],
      image: '/src/assets/img/羊.png'
    },
    {
      name: '吴九养殖场',
      value: 78,
      point: [106.28, 38.18],
      type: 0,
      address: '永宁县望远镇中心街18号',
      details: [
        { time: '2025-8-28 12:45:18', level: '三级/四级' }
      ],
      image: '/src/assets/img/牛.png'
    },
    {
      name: '郑十农场',
      value: 156,
      point: [106.08, 38.28],
      type: 1,
      address: '永宁县红寺堡镇东街25号',
      details: [
        { time: '2025-8-28 11:30:45', level: '一级/二级' },
        { time: '2025-8-28 10:15:33', level: '二级/三级' }
      ],
      image: '/src/assets/img/猪.png'
    }
  ]
  
  const pointSeriesData: any = []
  
  districtData.forEach((item: any) => {
    // 地图打点图标
    const mapPoint = {
      type: 'scatter',
      coordinateSystem: 'geo',
      geoIndex: 0,
      zlevel: 5,
      label: {
        show: false
      },
      symbol: `image://` + mapIconList[item.type],
      symbolSize: [40, 40],
      symbolOffset: [0, 0],
      z: 999,
      data: [
        {
          name: item.name,
          value: [item.point[0], item.point[1], item.value],
          itemStyle: {
            color: 'transparent'
          },
          // 添加完整的数据信息用于点击事件
          farmData: {
            name: item.name,
            address: item.address,
            details: item.details,
            image: item.image,
            value: item.value
          }
        }
      ],
      tooltip: {
        show: false  // 禁用默认tooltip，使用自定义悬浮框
      }
    }
    
    // 底部光圈效果
    const rippleEffect = {
      type: 'effectScatter',
      coordinateSystem: 'geo',
      geoIndex: 0,
      zlevel: 4,
      showEffectOn: 'render',
      rippleEffect: {
        scale: 3,
        brushType: 'stroke'
      },
      symbol: 'circle',
      symbolSize: [15, 15],
      itemStyle: {
        color: 'rgba(0, 226, 180, 0.8)',
        shadowBlur: 15,
        shadowColor: 'rgba(0, 226, 180, 0.6)'
      },
      data: [
        {
          name: item.name,
          value: [item.point[0], item.point[1], item.value]
        }
      ]
    }
    
    pointSeriesData.push(mapPoint)
    pointSeriesData.push(rippleEffect)
  })
  
  return pointSeriesData
}


