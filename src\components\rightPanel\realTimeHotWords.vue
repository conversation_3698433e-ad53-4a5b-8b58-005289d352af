<!-- 疫病管理  -->
<template>
  <CPanel>
    <template #header>疫病管理</template>
    <template #content>
      <div class="content-container">
        <!-- 左边仪表盘 -->
        <div class="gauge-section">
          <CEcharts :option="gaugeOption" />
        </div>
        <!-- 右边数据列表 -->
        <div class="list-section">
          <div class="list-item" v-for="(item, index) in listData" :key="index">
            <div class="bullet"></div>
            <div class="item-content">
              <span class="item-name">{{ item.name }}</span>
              <span class="item-value">{{ item.value }} 头</span>
            </div>
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'

const gaugeOption = ref<any>({})
const listData = ref([
  { name: '疫病名称1', value: 256 },
  { name: '疫病名称2', value: 137 },
  { name: '疫病名称3', value: 87 },
  { name: '疫病名称4', value: 56 },
  { name: '疫病名称5', value: 24 }
])

const initGaugeChart = () => {
  const percentage = 17.25; // 进度百分比

  const options = {
    title: {
      text: `${percentage}%`,
      subtext: '体育意识',
      x: 'center',
      y: 'center',
      textStyle: {
        fontSize: 24,
        fontWeight: 'bold',
        color: '#ffffff'
      },
      subtextStyle: {
        color: '#ffffff',
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    series: [
      {
        // 背景圆环
        name: 'background',
        type: 'pie',
        radius: ['70%', '85%'],
        silent: true,
        clockwise: true,
        startAngle: 90,
        z: 1,
        zlevel: 1,
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        data: [
          {
            value: 100,
            itemStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          }
        ]
      },
      {
        // 进度圆环
        name: 'progress',
        type: 'pie',
        radius: ['70%', '85%'],
        silent: true,
        clockwise: true,
        startAngle: 90,
        z: 2,
        zlevel: 2,
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        data: [
          {
            value: percentage,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: '#00ff88'
                  },
                  {
                    offset: 1,
                    color: '#00d4aa'
                  }
                ]
              }
            }
          },
          {
            value: 100 - percentage,
            itemStyle: {
              color: 'transparent'
            }
          }
        ]
      },
      {
        // 外圈刻度
        name: 'scale',
        type: 'pie',
        radius: ['88%', '90%'],
        silent: true,
        clockwise: true,
        startAngle: 90,
        z: 3,
        zlevel: 3,
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        data: Array.from({ length: 100 }, (_, i) => ({
          value: 1,
          itemStyle: {
            color: i % 2 === 0 ? 'rgba(255, 255, 255, 0.3)' : 'transparent'
          }
        }))
      }
    ]
  };

  return options;
}

onMounted(() => {
  gaugeOption.value = initGaugeChart()
})
</script>

<style lang="scss" scoped>
::v-deep .panel-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.content-container {
  display: flex;
  width: 100%;
  height: 200px;
  gap: 20px;
  padding: 0 12px 14px 12px;
}

.gauge-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 180px;
}

.list-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 12px;
}

.list-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.bullet {
  width: 8px;
  height: 8px;
  background: #58D9F8;
  border-radius: 2px;
  flex-shrink: 0;
}

.item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.item-name {
  color: #fff;
  font-size: 14px;
}

.item-value {
  color: #00FF88;
  font-size: 16px;
  font-weight: 500;
}
</style>
