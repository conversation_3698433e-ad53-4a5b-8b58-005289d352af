<!-- 景区排名 -->
<template>
  <CPanel>
    <template #header>景区排名</template>
    <template #content>
      <div class="content">
        <div class="left-section">
          <div class="center-circle">
            <div class="total-number">2563</div>
            <div class="total-label">访问总人次</div>
          </div>
          <div class="chart-container" id="ranking-chart"></div>
        </div>
        <div class="right-section">
          <div class="legend-container">
            <div class="legend-column">
              <div class="legend-item" v-for="(item, index) in leftColumn" :key="index">
                <div class="color-box" :style="{ backgroundColor: item.color }"></div>
                <div class="legend-content">
                  <div class="legend-main">
                    <span class="legend-text">{{ item.name }}</span>
                    <span class="trend-indicator" :class="item.trend">
                      <i v-if="item.trend === 'up'" class="trend-up">↗</i>
                      <i v-else-if="item.trend === 'down'" class="trend-down">↘</i>
                      <i v-else class="trend-stable">→</i>
                    </span>
                  </div>
                  <div class="legend-details">
                    <span class="percent">{{ item.percent }}%</span>
                    <span class="count">{{ item.count }}人</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="legend-column">
              <div class="legend-item" v-for="(item, index) in rightColumn" :key="index">
                <div class="color-box" :style="{ backgroundColor: item.color }"></div>
                <div class="legend-content">
                  <div class="legend-main">
                    <span class="legend-text">{{ item.name }}</span>
                    <span class="trend-indicator" :class="item.trend">
                      <i v-if="item.trend === 'up'" class="trend-up">↗</i>
                      <i v-else-if="item.trend === 'down'" class="trend-down">↘</i>
                      <i v-else class="trend-stable">→</i>
                    </span>
                  </div>
                  <div class="legend-details">
                    <span class="percent">{{ item.percent }}%</span>
                    <span class="count">{{ item.count }}人</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import CPanel from '@/components/common/CPanel.vue'

const statsList = ref([
  { name: '生态观光', percent: 28, color: '#E83F46', count: 718, trend: 'up' },
  { name: '文化体验', percent: 24, color: '#FF8D39', count: 615, trend: 'up' },
  { name: '休闲娱乐', percent: 22, color: '#FFD339', count: 564, trend: 'down' },
  { name: '农业采摘', percent: 18, color: '#B4E61A', count: 461, trend: 'up' },
  { name: '民俗体验', percent: 16, color: '#0DC869', count: 410, trend: 'up' },
  { name: '科普教育', percent: 14, color: '#00D4DC', count: 359, trend: 'stable' },
  { name: '户外探险', percent: 12, color: '#4F7DFF', count: 308, trend: 'up' },
  { name: '温泉养生', percent: 10, color: '#AF48FF', count: 256, trend: 'down' },
  { name: '古村游览', percent: 8, color: '#FF6B9D', count: 205, trend: 'stable' },
  { name: '湿地公园', percent: 6, color: '#00E2B4', count: 154, trend: 'up' },
  { name: '森林公园', percent: 5, color: '#FFA726', count: 128, trend: 'up' },
  { name: '博物馆', percent: 4, color: '#9C27B0', count: 103, trend: 'stable' }
])

// 将数据分为左右两列
const leftColumn = computed(() => statsList.value.slice(0, 6))
const rightColumn = computed(() => statsList.value.slice(6, 12))

const initChart = () => {
  const chartDom = document.getElementById('ranking-chart')
  if (chartDom) {
    // 清除之前的图表实例
    echarts.dispose(chartDom)
    const myChart = echarts.init(chartDom)
    const option = {
      tooltip: {
        trigger: 'item',
        formatter: function (params: any) {
          return params.seriesName + "</br>" +
            params.marker + "" + params.data.name + "</br>" +
            "数量：" + params.data.count + "</br>" +
            "占比：" + params.percent + "%"
        }
      },
      legend: {
        show: false // 隐藏默认图例，使用自定义图例
      },
      series: [{
        name: '景区排名',
        type: 'pie',
        radius: ['45%', '75%'], // 调整半径，让圆环更合适
        center: ['32%', '50%'],
        clockwise: false,
        avoidLabelOverlap: false,
        data: statsList.value.map(item => ({
          value: item.percent,
          name: item.name,
          count: item.count,
          itemStyle: { color: item.color }
        })),
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    }
    myChart.setOption(option, true) // 强制重新渲染
  }
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<style lang="scss" scoped>
.content {
  height: 150px;
  position: relative;
  padding: 10px;
  display: flex;
  align-items: center;
}

.left-section {
  position: relative;
  width: 60%;
  height: 100%;

  .center-circle {
    position: absolute;
    z-index: 2;
    left: 33%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 2px solid rgba(0, 212, 220, 0.5);
    box-shadow: 0 0 20px rgba(0, 212, 220, 0.3);

    .total-number {
      font-size: 28px;
      font-weight: bold;
      color: #fff;
      line-height: 1;
      margin-bottom: 6px;
      text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
    }

    .total-label {
      font-size: 11px;
      color: rgba(255, 255, 255, 0.9);
      line-height: 1;
      text-align: center;
    }
  }
}

.chart-container {
  width: 100%;
  height: 100%;
}

.right-section {
  width: 40%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.legend-container {
  display: flex;
  gap: 20px;
  height: 100%;
  align-items: center;
}

.legend-column {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-box {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  flex-shrink: 0;
}

.legend-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  white-space: nowrap;
}
</style>
