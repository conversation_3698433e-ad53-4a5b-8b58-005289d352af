<!-- 行情资讯 -->
<template>
  <CPanel>
    <template #header>行情资讯</template>
    <template #content>
      <div class="market-cards">
        <div class="market-card">
          <div class="card-icon">
            <img src="@/assets/img/活牛价格.png" alt="活牛价格" />
          </div>
          <div class="card-label">活牛价格</div>
          <div class="card-value">4256</div>
        </div>

        <div class="market-card">
          <div class="card-icon">
            <img src="@/assets/img/私聊原料.png" alt="饲料原料" />
          </div>
          <div class="card-label">饲料原料</div>
          <div class="card-value">2250</div>
        </div>

        <div class="market-card">
          <div class="card-icon">
            <img src="@/assets/img/农业新闻.png" alt="农业新闻" />
          </div>
          <div class="card-label">农业新闻</div>
          <div class="card-value">852</div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import CPanel from '@/components/common/CPanel.vue'
</script>

<style lang="scss" scoped>
.market-cards {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  gap: 16px;
}

.market-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50px;
  height: 100%;
  position: relative;

  .card-icon {
    margin-bottom: 8px;

    img {
      width: 8rem;
    }
  }

  .card-label {
    font-size: 14px;
    color: #37E4C9;
    margin-bottom: 8px;
    text-align: center;
  }

  .card-value {
    font-size: 14px;
    font-weight: bold;
    color: #FFFFFF;
    text-align: center;
  }
}

// 小背景框的颜色样式
.market-card {
  background: linear-gradient(135deg, #00CED1 0%, #008B8B 50%, #006666 100%);
  border-radius: 8px;
  box-shadow:
    0 0 20px rgba(0, 206, 209, 0.4),
    0 0 40px rgba(0, 206, 209, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid #00CED1;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 8px;
    background: linear-gradient(135deg,
      rgba(0, 206, 209, 0.3) 0%,
      rgba(0, 139, 139, 0.2) 50%,
      rgba(0, 102, 102, 0.1) 100%);
    z-index: -1;
  }

  &::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 10px;
    background: linear-gradient(135deg, #00CED1, #008B8B, #006666);
    z-index: -2;
    opacity: 0.6;
    filter: blur(4px);
  }
}
</style>
